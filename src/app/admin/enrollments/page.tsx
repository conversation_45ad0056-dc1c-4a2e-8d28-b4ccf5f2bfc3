'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState, useCallback, Suspense } from 'react';
import { toast } from 'sonner';
import { isTeacherOrAdmin } from '@/lib/roles';
import { enrollmentsService } from '@/lib/services/enrollments.service';

// Icons
import { 
  Users, 
  Search, 
  Plus, 
  ArrowLeft, 
  Loader2, 
  UserPlus,
  BookOpen,
  Calendar,
  CheckCircle,
  AlertCircle,
  Phone,
  Mail,
  User
} from 'lucide-react';

// Components
import {
  AdminPageLayout,
  FormCard,
  ModernInput,
  ModernButton,
  ModernSelect,
  Button,
  Badge
} from '@/components/ui/modern';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { useConfirmationDialog } from '@/components/ui/confirmation-dialog';

interface Student {
  id: string;
  name: string;
  email: string | null;
  phone: string | null;
  status: string;
  enrollments: Array<{
    id: string;
    status: string;
    type: string;
    course: {
      id: string;
      name: string;
    };
    class: {
      id: string;
      name: string;
    } | null;
  }>;
}

interface Course {
  id: string;
  name: string;
  allowsMakeup: boolean;
  _count: {
    classes: number;
    enrollments: number;
  };
}

interface Class {
  id: string;
  name: string;
  courseId: string;
  startDate: string;
  endDate: string | null;
  maxStudents: number | null;
  _count: {
    enrollments: number;
  };
}

interface EnrollmentFormData {
  studentId: string;
  courseId: string;
  classId: string;
  type: 'regular' | 'guest' | 'restart';
  notes: string;
}

function EnrollmentsPageContent() {
  const { user, loading } = useAuth();
  const router = useRouter();
  
  // State
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [students, setStudents] = useState<Student[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [classes, setClasses] = useState<Class[]>([]);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [formData, setFormData] = useState<EnrollmentFormData>({
    studentId: '',
    courseId: '',
    classId: '',
    type: 'regular',
    notes: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [step, setStep] = useState<'search' | 'form'>('search');

  const { ConfirmationDialog, confirm } = useConfirmationDialog();

  // Fetch functions
  const fetchStudents = useCallback(async (search: string = '') => {
    try {
      const response = await fetch(`/api/students/all?search=${encodeURIComponent(search)}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Falha ao carregar alunos');
      }

      const result = await response.json();
      setStudents(result.data || []);
    } catch (error) {
      console.error('Error fetching students:', error);
      toast.error('Erro ao carregar alunos');
      setStudents([]);
    }
  }, []);

  const fetchCourses = useCallback(async () => {
    try {
      const response = await fetch('/api/courses', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Falha ao carregar cursos');
      }

      const result = await response.json();
      setCourses(result.data || []);
    } catch (error) {
      console.error('Error fetching courses:', error);
      toast.error('Erro ao carregar cursos');
      setCourses([]);
    }
  }, []);

  const fetchClasses = useCallback(async (courseId: string) => {
    if (!courseId) {
      setClasses([]);
      return;
    }

    try {
      const response = await fetch(`/api/classes?courseId=${courseId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Falha ao carregar turmas');
      }

      const result = await response.json();
      setClasses(result.data || []);
    } catch (error) {
      console.error('Error fetching classes:', error);
      toast.error('Erro ao carregar turmas');
      setClasses([]);
    }
  }, []);

  // Effects
  useEffect(() => {
    if (!loading && !isTeacherOrAdmin(user)) {
      router.push('/');
      return;
    }

    if (isTeacherOrAdmin(user)) {
      fetchStudents('');
      fetchCourses();
      setIsLoading(false);
    }
  }, [user, loading, router, fetchStudents, fetchCourses]);

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (step === 'search') {
        fetchStudents(searchTerm);
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchTerm, step, fetchStudents]);

  // Fetch classes when course changes
  useEffect(() => {
    if (formData.courseId) {
      fetchClasses(formData.courseId);
      setFormData(prev => ({ ...prev, classId: '' })); // Reset class selection
    }
  }, [formData.courseId, fetchClasses]);

  // Loading state
  if (loading || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="mx-auto animate-spin" size={48} />
          <p className="mt-4 text-muted-foreground">Carregando...</p>
        </div>
      </div>
    );
  }

  // Access control
  if (!isTeacherOrAdmin(user)) {
    return null;
  }

  // Handler functions will be added in the next part
  const handleStudentSelect = (student: Student) => {
    setSelectedStudent(student);
    setFormData(prev => ({ ...prev, studentId: student.id }));
    setStep('form');
  };

  const handleBackToSearch = () => {
    setStep('search');
    setSelectedStudent(null);
    setFormData({
      studentId: '',
      courseId: '',
      classId: '',
      type: 'regular',
      notes: ''
    });
  };

  const navigateToCreateStudent = () => {
    router.push('/admin/students?mode=new');
  };

  // Render search step
  if (step === 'search') {
    return (
      <AdminPageLayout
        title="Matricular Aluno"
        description="Selecione um aluno para matricular em uma turma"
        icon={UserPlus}
        backUrl="/"
        actionButton={{
          label: 'Criar Aluno',
          onClick: navigateToCreateStudent,
          icon: Plus
        }}
      >
        {/* Search Section */}
        <Card className="mb-6 shadow-lg border-0">
          <CardContent className="pt-6">
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Buscar alunos por nome, email ou telefone..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="border-2 border-gray-200 focus:border-[#667eea] focus:ring-4 focus:ring-[#667eea]/20 transition-all duration-300"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Students List */}
        <Card className="shadow-lg border-0">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="w-5 h-5 mr-2 text-[#667eea]" />
              Alunos Disponíveis
            </CardTitle>
            <CardDescription>
              Selecione um aluno para matricular
            </CardDescription>
          </CardHeader>
          <CardContent>
            {students.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-20 h-20 bg-gradient-to-br from-[#667eea] to-[#764ba2] rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-xl">
                  <Users className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  Nenhum aluno encontrado
                </h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  {searchTerm ? 'Tente uma busca diferente.' : 'Comece criando um novo aluno.'}
                </p>
                <Button 
                  onClick={navigateToCreateStudent} 
                  className="bg-gradient-to-r from-[#667eea] to-[#764ba2] hover:from-[#5a6fd8] hover:to-[#6b4190] text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300"
                  size="lg"
                >
                  <Plus className="w-5 h-5 mr-2" />
                  Criar Primeiro Aluno
                </Button>
              </div>
            ) : (
              <div className="grid gap-4">
                {students.map((student) => (
                  <div
                    key={student.id}
                    className="p-4 border border-gray-200 rounded-lg hover:border-[#667eea] hover:shadow-md transition-all duration-300 cursor-pointer"
                    onClick={() => handleStudentSelect(student)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-br from-[#667eea] to-[#764ba2] rounded-full flex items-center justify-center">
                            <User className="w-5 h-5 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">{student.name}</h3>
                            <div className="flex items-center space-x-4 text-sm text-gray-600">
                              {student.email && (
                                <div className="flex items-center">
                                  <Mail className="w-4 h-4 mr-1" />
                                  {student.email}
                                </div>
                              )}
                              {student.phone && (
                                <div className="flex items-center">
                                  <Phone className="w-4 h-4 mr-1" />
                                  {student.phone}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={student.status === 'active' ? 'default' : 'secondary'}>
                          {student.status === 'active' ? 'Ativo' : 'Inativo'}
                        </Badge>
                        <Badge variant="outline">
                          {student.enrollments.length} matrículas
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <ConfirmationDialog />
      </AdminPageLayout>
    );
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.studentId || !formData.courseId || !formData.classId) {
      toast.error('Todos os campos são obrigatórios');
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await enrollmentsService.createEnrollment({
        studentId: formData.studentId,
        courseId: formData.courseId,
        classId: formData.classId,
        type: formData.type,
        notes: formData.notes.trim() || undefined
      });

      if (result.success) {
        toast.success('Aluno matriculado com sucesso!');
        router.push('/admin/classes');
      } else {
        throw new Error(result.error || 'Erro ao matricular aluno');
      }
    } catch (error) {
      console.error('Error creating enrollment:', error);
      toast.error(error instanceof Error ? error.message : 'Erro ao matricular aluno');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Check if student is already enrolled in the selected course
  const isStudentEnrolledInCourse = (courseId: string) => {
    if (!selectedStudent) return false;
    return selectedStudent.enrollments.some(
      enrollment => enrollment.course.id === courseId && enrollment.status === 'active'
    );
  };

  // Get available spots for a class
  const getAvailableSpots = (classItem: Class) => {
    if (!classItem.maxStudents) return 'Ilimitado';
    const available = classItem.maxStudents - classItem._count.enrollments;
    return available > 0 ? `${available} vagas` : 'Lotada';
  };

  // Check if class is full
  const isClassFull = (classItem: Class) => {
    if (!classItem.maxStudents) return false;
    return classItem._count.enrollments >= classItem.maxStudents;
  };

  // Form step
  return (
    <AdminPageLayout
      title="Matricular Aluno"
      description={`Matriculando: ${selectedStudent?.name}`}
      icon={UserPlus}
      backUrl="/admin/enrollments"
    >
      <FormCard title="Dados da Matrícula" icon={UserPlus}>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Student Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-[#667eea] to-[#764ba2] rounded-full flex items-center justify-center">
                <User className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">{selectedStudent?.name}</h3>
                <div className="flex items-center space-x-4 text-sm text-gray-600">
                  {selectedStudent?.email && (
                    <div className="flex items-center">
                      <Mail className="w-4 h-4 mr-1" />
                      {selectedStudent.email}
                    </div>
                  )}
                  {selectedStudent?.phone && (
                    <div className="flex items-center">
                      <Phone className="w-4 h-4 mr-1" />
                      {selectedStudent.phone}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Course Selection */}
          <ModernSelect
            label="Curso"
            value={formData.courseId}
            onChange={(value) => setFormData({ ...formData, courseId: value })}
            icon={BookOpen}
            required
          >
            <option value="">Selecione um curso</option>
            {courses.map((course) => (
              <option key={course.id} value={course.id}>
                {course.name} ({course._count.classes} turmas)
              </option>
            ))}
          </ModernSelect>

          {/* Show warning if student is already enrolled */}
          {formData.courseId && isStudentEnrolledInCourse(formData.courseId) && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center">
                <AlertCircle className="w-5 h-5 text-yellow-600 mr-2" />
                <p className="text-yellow-800">
                  <strong>Atenção:</strong> Este aluno já possui uma matrícula ativa neste curso.
                </p>
              </div>
            </div>
          )}

          {/* Class Selection */}
          {formData.courseId && (
            <ModernSelect
              label="Turma"
              value={formData.classId}
              onChange={(value) => setFormData({ ...formData, classId: value })}
              icon={Calendar}
              required
            >
              <option value="">Selecione uma turma</option>
              {classes.map((classItem) => (
                <option
                  key={classItem.id}
                  value={classItem.id}
                  disabled={isClassFull(classItem)}
                >
                  {classItem.name} - {getAvailableSpots(classItem)}
                  {isClassFull(classItem) ? ' (Lotada)' : ''}
                </option>
              ))}
            </ModernSelect>
          )}

          {/* Enrollment Type */}
          <ModernSelect
            label="Tipo de Matrícula"
            value={formData.type}
            onChange={(value) => setFormData({ ...formData, type: value as 'regular' | 'guest' | 'restart' })}
            icon={CheckCircle}
            required
          >
            <option value="regular">Regular</option>
            <option value="guest">Convidado</option>
            <option value="restart">Recomeço</option>
          </ModernSelect>

          {/* Notes */}
          <ModernInput
            label="Observações (opcional)"
            value={formData.notes}
            onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
            placeholder="Observações sobre a matrícula..."
            icon={User}
          />

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
            <Button
              type="button"
              variant="outline"
              onClick={handleBackToSearch}
              className="border-2 border-gray-300 text-gray-700 hover:bg-gray-50 transition-all duration-300"
            >
              Voltar
            </Button>
            <ModernButton
              type="submit"
              loading={isSubmitting}
              disabled={!formData.studentId || !formData.courseId || !formData.classId}
              icon={CheckCircle}
            >
              Matricular Aluno
            </ModernButton>
          </div>
        </form>
      </FormCard>

      <ConfirmationDialog />
    </AdminPageLayout>
  );
}

export default function EnrollmentsPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="mx-auto animate-spin" size={48} />
          <p className="mt-4 text-muted-foreground">Carregando...</p>
        </div>
      </div>
    }>
      <EnrollmentsPageContent />
    </Suspense>
  );
}
