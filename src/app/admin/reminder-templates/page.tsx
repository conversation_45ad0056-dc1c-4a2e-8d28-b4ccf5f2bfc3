'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState, useCallback, Suspense } from 'react';
import { toast } from 'sonner';
import { templatesService, ReminderTemplate } from '@/lib/services/templates.service';
import { isAdminOrSuperAdmin } from '@/lib/roles';
import { useAutoSave } from '@/hooks/useAutoSave';

// Components
import {
  AdminPageLayout,
  FormCard,
  ModernInput,
  ModernTextarea,
  ModernSelect,
  Button,
  Badge
} from '@/components/ui/modern';
import { AutoSaveIndicator } from '@/components/ui/auto-save-indicator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { Switch } from '@/components/ui/switch';

// Icons
import { 
  Loader2, MessageSquare, ArrowLeft, Plus, Search, Edit, Trash2, 
 FileText, Tag, MessageCircle
} from 'lucide-react';

interface TemplateFormData {
  name: string;
  category: string;
  template: string;
  description: string;
  isActive: boolean;
}

const categoryOptions = [
  { value: 'aula', label: 'Aula' },
  { value: 'mentoria', label: 'Mentoria' },
  { value: 'reposicao', label: 'Reposição' },
  { value: 'geral', label: 'Geral' },
  { value: 'cobranca', label: 'Cobrança' },
  { value: 'promocional', label: 'Promocional' }
];

function ReminderTemplatesPageContent() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Get mode and ID from URL parameters
  const mode = searchParams.get('mode') || 'list'; // list, new, edit
  const templateId = searchParams.get('id');
  
  // State
  const [templates, setTemplates] = useState<ReminderTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<ReminderTemplate | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState<string | null>(null);
  const { showConfirmation, ConfirmationDialog } = useConfirmationDialog();
  
  const [formData, setFormData] = useState<TemplateFormData>({
    name: '',
    category: '',
    template: '',
    description: '',
    isActive: true
  });

  // Navigation helpers
  const navigateToMode = useCallback((newMode: string, id?: string) => {
    const params = new URLSearchParams();
    params.set('mode', newMode);
    if (id) params.set('id', id);
    router.push(`/admin/reminder-templates?${params.toString()}`);
  }, [router]);

  const navigateToList = useCallback(() => {
    router.push('/admin/reminder-templates');
  }, [router]);

  // Data fetching
  const fetchTemplates = useCallback(async (search?: string) => {
    try {
      // Only show loading for table content, not full page
      if (mode === 'list') {
        setIsLoading(true);
      }
      setError(null);
      const response = await templatesService.getTemplates(search || '');
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch reminder templates');
      }

      setTemplates(response.data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      // Only hide loading for table content
      if (mode === 'list') {
        setIsLoading(false);
      }
    }
  }, [mode]);

  const fetchTemplate = useCallback(async (id: string) => {
    try {
      setIsLoading(true);
      const response = await templatesService.getTemplate(id);

      if (!response.success) {
        throw new Error(response.error || 'Falha ao carregar template');
      }

      const templateData = response.data;
      setSelectedTemplate(templateData);

      // Populate form for editing
      setFormData({
        name: templateData.name ?? '',
        category: templateData.category ?? '',
        template: templateData.template ?? '',
        description: templateData.description ?? '',
        isActive: templateData.isActive !== false
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // AutoSave function for edit mode
  const autoSaveTemplate = useCallback(async (data: TemplateFormData) => {
    if (!templateId || mode !== 'edit') return;

    const payload = {
      name: data.name.trim(),
      category: data.category || undefined,
      template: data.template.trim(),
      description: data.description.trim() || undefined,
      isActive: data.isActive
    };

    const response = await templatesService.updateTemplate(templateId, payload);

    if (!response.success) {
      throw new Error(response.error || 'Falha ao salvar template');
    }

    return response;
  }, [templateId, mode]);

  // AutoSave hook for edit mode
  const autoSaveState = useAutoSave({
    data: formData,
    onSave: autoSaveTemplate,
    config: { debounceMs: 2000, enableToast: false },
    isValid: (data) => !!data.name.trim() && !!data.template.trim(),
    enabled: mode === 'edit'
  });

  // Effects
  useEffect(() => {
    if (!loading && !isAdminOrSuperAdmin(user)) {
      router.push('/');
      return;
    }

    if (isAdminOrSuperAdmin(user)) {
      if (mode === 'edit' && templateId) {
        fetchTemplate(templateId);
      } else if (mode === 'list') {
        fetchTemplates('');
      } else if (mode === 'new') {
        setIsLoading(false);
        // Reset form for new template
        setFormData({
          name: '',
          category: '',
          template: '',
          description: '',
          isActive: true
        });
      }
    }
  }, [user, loading, router, mode, templateId, fetchTemplate, fetchTemplates]);

  // Form handlers
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('Nome é obrigatório');
      return;
    }

    if (!formData.template.trim()) {
      toast.error('Template é obrigatório');
      return;
    }

    try {
      setIsSubmitting(true);

      const payload = {
        name: formData.name.trim(),
        category: formData.category || undefined,
        template: formData.template.trim(),
        description: formData.description.trim() || undefined,
        isActive: formData.isActive
      };

      if (mode === 'new') {
        const response = await templatesService.createTemplate(payload);

        if (!response.success) {
          throw new Error(response.error || 'Falha ao criar template');
        }

        toast.success('Template criado com sucesso!');
        navigateToList();
      } else if (mode === 'edit' && templateId) {
        // In edit mode, just navigate since autosave handles saving
        navigateToList();
      }
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Erro ao salvar template');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSearch = useCallback(() => {
    fetchTemplates(searchTerm);
  }, [fetchTemplates, searchTerm]);

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (mode === 'list') {
        fetchTemplates(searchTerm);
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchTerm, mode, fetchTemplates]);

  const handleDelete = (templateId: string, templateName: string) => {
    showConfirmation({
      title: 'Excluir Template',
      description: `Tem certeza que deseja excluir o template "${templateName}"? Esta ação não pode ser desfeita.`,
      confirmText: 'Excluir',
      cancelText: 'Cancelar',
      variant: 'destructive',
      icon: 'delete',
      onConfirm: async () => {
        try {
          const response = await templatesService.deleteTemplate(templateId);

          if (!response.success) {
            throw new Error(response.error || 'Failed to delete template');
          }

          toast.success('Template excluído com sucesso!');
          fetchTemplates(searchTerm);
        } catch (err) {
          toast.error(err instanceof Error ? err.message : 'Erro ao excluir template');
        }
      }
    });
  };

  // Utility functions
  const getCategoryBadgeVariant = (category?: string) => {
    if (!category) return 'secondary';
    switch (category.toLowerCase()) {
      case 'aula':
        return 'info';
      case 'mentoria':
        return 'warning';
      case 'reposicao':
        return 'secondary';
      case 'cobranca':
        return 'destructive';
      case 'promocional':
        return 'success';
      default:
        return 'secondary';
    }
  };

  const getCategoryLabel = (category?: string) => {
    if (!category) return 'Sem categoria';
    const option = categoryOptions.find(opt => opt.value === category.toLowerCase());
    return option?.label || category;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  // Loading state - only show full page loading for non-list modes
  if (loading || (isLoading && mode !== 'list')) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="mx-auto animate-spin" size={48} />
          <p className="mt-4 text-muted-foreground">
            {mode === 'edit' ? 'Carregando template...' : 'Carregando templates...'}
          </p>
        </div>
      </div>
    );
  }

  // Permission check
  if (!isAdminOrSuperAdmin(user)) {
    return null;
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-destructive">Erro</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error}</p>
            <Button onClick={() => navigateToList()} className="mt-4">
              Voltar para Lista
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Render based on mode
  if (mode === 'new') {
    return (
      <AdminPageLayout
        title="Novo Template"
        description="Criar um novo template de mensagem"
        icon={MessageCircle}
        backUrl="/admin/reminder-templates"
      >
        <FormCard title="Cadastrar Novo Template" icon={MessageSquare}>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 xl:grid-cols-3">
              <ModernInput
                label="Nome do Template"
                value={formData.name}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Ex: Lembrete de Aula, Confirmação de Matrícula..."
                icon={MessageSquare}
                required
                className="md:col-span-2 xl:col-span-1"
              />

              <ModernSelect
                label="Categoria"
                value={formData.category}
                onValueChange={(value) => setFormData({ ...formData, category: value })}
                icon={Tag}
                options={categoryOptions}
                placeholder="Selecione uma categoria"
              />
            </div>

            <ModernTextarea
              label="Descrição"
              value={formData.description}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Descrição do template e quando usar..."
              rows={2}
              icon={FileText}
            />

            <ModernTextarea
              label="Template da Mensagem"
              value={formData.template}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setFormData({ ...formData, template: e.target.value })}
              placeholder="Digite o template da mensagem. Use {{variavel}} para campos dinâmicos..."
              rows={6}
              icon={MessageSquare}
              required
            />

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">Variáveis Disponíveis:</h4>
              <div className="text-sm text-blue-700 grid grid-cols-2 md:grid-cols-3 gap-2">
                <span>{'{{studentName}}'}</span>
                <span>{'{{className}}'}</span>
                <span>{'{{courseName}}'}</span>
                <span>{'{{lessonDate}}'}</span>
                <span>{'{{lessonTime}}'}</span>
                <span>{'{{teacherName}}'}</span>
              </div>
            </div>

          </form>
        </FormCard>
      </AdminPageLayout>
    );
  }

  if (mode === 'edit' && selectedTemplate) {
    return (
      <AdminPageLayout
        title="Editar Template"
        description={`Edite o template ${selectedTemplate.name}`}
        icon={MessageSquare}
        backUrl="/admin/reminder-templates"
      >
        <FormCard 
          title="Informações do Template" 
          icon={MessageSquare}
          rightContent={
            <AutoSaveIndicator
              isAutoSaving={autoSaveState.isAutoSaving}
              lastSaved={autoSaveState.lastSaved}
              hasUnsavedChanges={autoSaveState.hasUnsavedChanges}
              error={autoSaveState.error}
            />
          }
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 xl:grid-cols-3">
              <ModernInput
                label="Nome do Template"
                value={formData.name}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Ex: Lembrete de Aula, Confirmação de Matrícula..."
                icon={MessageSquare}
                required
                className="md:col-span-2 xl:col-span-1"
              />

              <ModernSelect
                label="Categoria"
                value={formData.category}
                onValueChange={(value) => setFormData({ ...formData, category: value })}
                icon={Tag}
                options={categoryOptions}
                placeholder="Selecione uma categoria"
              />
            </div>

            <ModernTextarea
              label="Descrição"
              value={formData.description}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Descrição do template e quando usar..."
              rows={2}
              icon={FileText}
            />

            <ModernTextarea
              label="Template da Mensagem"
              value={formData.template}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setFormData({ ...formData, template: e.target.value })}
              placeholder="Digite o template da mensagem. Use {{variavel}} para campos dinâmicos..."
              rows={6}
              icon={MessageSquare}
              required
            />

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">Variáveis Disponíveis:</h4>
              <div className="text-sm text-blue-700 grid grid-cols-2 md:grid-cols-3 gap-2">
                <span>{'{{studentName}}'}</span>
                <span>{'{{className}}'}</span>
                <span>{'{{courseName}}'}</span>
                <span>{'{{lessonDate}}'}</span>
                <span>{'{{lessonTime}}'}</span>
                <span>{'{{teacherName}}'}</span>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
              />
              <label htmlFor="isActive" className="text-sm font-medium">
                Template ativo
              </label>
            </div>

            <div className="flex justify-end">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigateToList()}
                className="flex items-center"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar para Lista
              </Button>
            </div>
          </form>
        </FormCard>
      </AdminPageLayout>
    );
  }

  // Default list view
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="w-full max-w-[1600px] mx-auto">
        {/* Header Section */}
        <div className="bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-2xl p-8 text-white shadow-xl mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button onClick={() => router.push('/')} variant="outline" size="sm" className="bg-white/20 text-white border-white/30 hover:bg-white/30">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar
              </Button>
              <div>
                <h1 className="text-3xl font-bold mb-2 flex items-center">
                  <MessageSquare className="w-8 h-8 mr-3" />
                  Templates de Lembretes
                </h1>
                <p className="text-white/90">
                  Gerenciar templates de mensagens automáticas
                </p>
              </div>
            </div>
            <div className="hidden md:block">
              <Button 
                onClick={() => navigateToMode('new')}
                className="bg-white/20 text-white border-white/30 hover:bg-white/30 backdrop-blur-sm"
                size="lg"
              >
                <Plus className="w-5 h-5 mr-2" />
                Novo Template
              </Button>
            </div>
          </div>
          {/* Mobile button */}
          <div className="md:hidden mt-4">
            <Button 
              onClick={() => navigateToMode('new')}
              className="bg-white/20 text-white border-white/30 hover:bg-white/30 backdrop-blur-sm w-full"
            >
              <Plus className="w-5 h-5 mr-2" />
              Novo Template
            </Button>
          </div>
        </div>

        {/* Search Section */}
        <Card className="mb-6 shadow-lg border-0">
          <CardContent className="pt-6">
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Buscar templates por nome, categoria ou conteúdo..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  className="border-2 border-gray-200 focus:border-[#667eea] focus:ring-4 focus:ring-[#667eea]/20 transition-all duration-300"
                />
              </div>
              <Button 
                onClick={handleSearch}
                className="bg-gradient-to-r from-[#667eea] to-[#764ba2] hover:from-[#5a6fd8] hover:to-[#6b4190] text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300"
              >
                <Search className="w-4 h-4 mr-2" />
                Buscar
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Templates Table */}
        <Card className="shadow-xl border-0">
          <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg">
            <CardTitle className="text-2xl font-bold text-gray-900 flex items-center">
              <MessageSquare className="w-6 h-6 mr-2 text-[#667eea]" />
              Templates Cadastrados
            </CardTitle>
            <CardDescription className="text-lg">
              {templates.length} template{templates.length !== 1 ? 's' : ''} encontrado{templates.length !== 1 ? 's' : ''}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading && mode === 'list' ? (
              <div className="text-center py-12">
                <Loader2 className="mx-auto animate-spin" size={32} />
                <p className="mt-4 text-muted-foreground">Carregando templates...</p>
              </div>
            ) : templates.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-20 h-20 bg-gradient-to-br from-[#667eea] to-[#764ba2] rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-xl">
                  <MessageSquare className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  Nenhum template encontrado
                </h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  {searchTerm ? 'Tente uma busca diferente ou ajuste os filtros.' : 'Comece criando um template para automatizar suas mensagens.'}
                </p>
                {!searchTerm && (
                  <Button 
                    onClick={() => navigateToMode('new')} 
                    className="bg-gradient-to-r from-[#667eea] to-[#764ba2] hover:from-[#5a6fd8] hover:to-[#6b4190] text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300"
                    size="lg"
                  >
                    <Plus className="w-5 h-5 mr-2" />
                    Criar Primeiro Template
                  </Button>
                )}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome</TableHead>
                      <TableHead>Categoria</TableHead>
                      <TableHead>Template</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Criado em</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {templates.map((template) => (
                      <TableRow key={template.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{template.name}</div>
                            {template.description && (
                              <div className="text-sm text-muted-foreground">
                                {template.description}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={getCategoryBadgeVariant(template.category)}>
                            {getCategoryLabel(template.category)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="max-w-xs">
                            <div className="text-sm text-muted-foreground truncate">
                              {template.template}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={template.isActive ? 'success' : 'secondary'}>
                            {template.isActive ? 'Ativo' : 'Inativo'}
                          </Badge>
                        </TableCell>
                        <TableCell>{formatDate(template.createdAt)}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              onClick={() => navigateToMode('edit', template.id)}
                              variant="outline"
                              size="sm"
                              className="border-[#667eea] text-[#667eea] hover:bg-[#667eea] hover:text-white transition-all duration-300"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              onClick={() => handleDelete(template.id, template.name)}
                              variant="outline"
                              size="sm"
                              className="border-red-500 text-red-500 hover:bg-red-500 hover:text-white transition-all duration-300"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      <ConfirmationDialog />
    </div>
  );
}

export default function ReminderTemplatesPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="mx-auto animate-spin" size={48} />
          <p className="mt-4 text-muted-foreground">Carregando...</p>
        </div>
      </div>
    }>
      <ReminderTemplatesPageContent />
    </Suspense>
  );
}