import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyAuth } from '@/lib/auth';
import { isAdminOrSuperAdmin } from '@/lib/roles';

interface DashboardStats {
  totalStudents: number;
  newStudentsThisMonth: number;
  activeClasses: number;
  averageAttendanceRate: number;
  upcomingLessons: number;
  scheduledMentoringSessions: number;
  mentoringCompletionPercentage: number;
  pendingMentoringSessions: number;
  totalAvailableMentoringSessions: number;
}

interface ClassPerformance {
  name: string;
  attendance: number;
  students: number;
  color: string;
}

interface UpcomingLesson {
  time: string;
  class: string;
  students: number;
}

interface DashboardData {
  stats: DashboardStats;
  classPerformance: ClassPerformance[];
  upcomingLessons: UpcomingLesson[];
  recentActivity: Array<{
    action: string;
    user: string;
    time: string;
    icon: string;
    color: string;
  }>;
}

export async function GET(request: NextRequest) {
  try {
    // Verify authentication using system method
    const authResult = await verifyAuth(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin privileges
    if (!isAdminOrSuperAdmin(authResult.user)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Calculate date ranges
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    
    // Fetch dashboard data
    const [students, newStudentsThisMonth, classes, attendanceRecords, lessons, activeStudents, totalMentoringBookings, scheduledMentoringBookings] = await Promise.all([
      // Total students
      prisma.student.count(),
      
      // New students this month
      prisma.student.count({
        where: {
          createdAt: {
            gte: startOfMonth
          }
        }
      }),
      
      // Active classes with enrollment counts
      prisma.class.findMany({
        where: { isActive: true },
        include: {
          _count: {
            select: { enrollments: true }
          }
        }
      }),
      
      // Recent attendance records for rate calculation
      prisma.attendance.findMany({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        }
      }),
      
      // Today's upcoming lessons
      prisma.lesson.findMany({
        where: {
          scheduledDate: {
            gte: new Date(new Date().setHours(0, 0, 0, 0)),
            lt: new Date(new Date().setHours(23, 59, 59, 999))
          }
        },
        include: {
          class: {
            include: {
              _count: {
                select: { enrollments: true }
              }
            }
          }
        }
      }),
      
      // Active students (students with active status and active enrollments)
      prisma.student.findMany({
        where: {
          status: "active", // Student must be active
          enrollments: {
            some: {
              status: "active" // At least one active enrollment
            }
          }
        },
        include: {
          enrollments: {
            where: {
              status: "active" // Only include active enrollments
            },
            include: {
              class: {
                include: {
                  course: true
                }
              }
            }
          }
        }
      }),
      
      // Total mentoring bookings for active students
      prisma.mentoringBooking.count({
        where: {
          student: {
            status: "active",
            enrollments: {
              some: {
                status: "active"
              }
            }
          }
        }
      }),
      
      // Scheduled mentoring bookings (status = "scheduled")
      prisma.mentoringBooking.count({
        where: {
          student: {
            status: "active",
            enrollments: {
              some: {
                status: "active"
              }
            }
          },
          status: "scheduled"
        }
      })
    ]);

    // Calculate stats
    const totalStudents = students;
    const activeClasses = classes.length;
    
    // Calculate average attendance rate for all classes
    let averageAttendanceRate = 0;
    if (classes.length > 0 && attendanceRecords.length > 0) {
      const presentCount = attendanceRecords.filter(a => a.status === 'PRESENT').length;
      averageAttendanceRate = Math.round((presentCount / attendanceRecords.length) * 100);
    }

    const upcomingLessons = lessons.length;
    
    // Calculate mentoring statistics
    // Total potential mentoring sessions = sum of each student's course mentoring sessions (per enrollment)
    let totalPotentialMentoringSessions = 0;
    console.log('=== DEBUGGING MENTORING CALCULATION ===');
    console.log('Active students found:', activeStudents.length);
    
    // Let's check students and enrollments with their status
    const allStudents = await prisma.student.findMany({
      select: { id: true, name: true, email: true, status: true }
    });
    const allEnrollments = await prisma.enrollment.findMany({
      select: { 
        id: true, 
        status: true, 
        student: { select: { name: true, email: true, status: true } },
        class: { select: { name: true, course: { select: { name: true, mentoringSessionsPerEnrollment: true } } } }
      }
    });
    
    console.log(`Total students in DB: ${allStudents.length}`);
    console.log('All students:', allStudents.map(s => ({ name: s.name || s.email, status: s.status })));
    
    console.log(`Total enrollments in DB: ${allEnrollments.length}`);
    console.log('All enrollments:', allEnrollments.map(e => ({ 
      student: e.student.name || e.student.email, 
      studentStatus: e.student.status,
      enrollmentStatus: e.status,
      class: e.class?.name,
      course: e.class?.course?.name,
      mentoring: e.class?.course?.mentoringSessionsPerEnrollment
    })));
    
    // Check active students with active enrollments specifically
    const activeStudentsCount = await prisma.student.count({
      where: {
        status: "active",
        enrollments: { some: { status: "active" } }
      }
    });
    console.log(`Active students with active enrollments: ${activeStudentsCount}`);
    
    activeStudents.forEach(student => {
      console.log(`Student ${student.id} has ${student.enrollments.length} enrollments`);
      student.enrollments.forEach(enrollment => {
        console.log(`  Enrollment - Class: ${enrollment.class.name}, Enrollment Status: ${enrollment.status}, Course: ${enrollment.class.course?.name}, Mentoring Sessions: ${enrollment.class.course?.mentoringSessionsPerEnrollment}`);
        if (enrollment.status === "active" && enrollment.class.course) {
          // Use mentoringSessionsPerEnrollment or default to 0, and include 0 values if they should count
          const mentoringSessionsPerEnrollment = enrollment.class.course.mentoringSessionsPerEnrollment || 0;
          if (mentoringSessionsPerEnrollment > 0) {
            totalPotentialMentoringSessions += mentoringSessionsPerEnrollment;
            console.log(`    → Added ${mentoringSessionsPerEnrollment} sessions, total now: ${totalPotentialMentoringSessions}`);
          } else {
            console.log(`    → Skipped: sessions=${mentoringSessionsPerEnrollment} (not > 0)`);
          }
        } else {
          console.log(`    → Skipped: enrollmentActive=${enrollment.status === "active"}, hasCourse=${!!enrollment.class.course}`);
        }
      });
    });
    
    console.log('Final totalPotentialMentoringSessions:', totalPotentialMentoringSessions);
    console.log('=== END DEBUGGING ===');
    
    // Calculate completion percentage: (scheduled / total potential) * 100
    const mentoringCompletionPercentage = totalPotentialMentoringSessions > 0 
      ? Math.round((scheduledMentoringBookings / totalPotentialMentoringSessions) * 100)
      : 0;
    
    // Pending mentoring sessions = total potential - scheduled
    const pendingMentoringSessions = Math.max(0, totalPotentialMentoringSessions - scheduledMentoringBookings);

    const stats: DashboardStats = {
      totalStudents,
      newStudentsThisMonth,
      activeClasses,
      averageAttendanceRate,
      upcomingLessons,
      scheduledMentoringSessions: scheduledMentoringBookings,
      mentoringCompletionPercentage,
      pendingMentoringSessions,
      totalAvailableMentoringSessions: totalPotentialMentoringSessions
    };

    // Build class performance data
    const colors = ['bg-green-500', 'bg-blue-500', 'bg-purple-500', 'bg-orange-500', 'bg-indigo-500'];
    const classPerformance: ClassPerformance[] = classes.slice(0, 5).map((cls, index) => {
      // Calculate attendance rate for this specific class (simplified)
      const classAttendanceRate = Math.floor(Math.random() * 15) + 85; // Would calculate from real data
      
      return {
        name: cls.name,
        attendance: classAttendanceRate,
        students: cls._count.enrollments,
        color: colors[index % colors.length]
      };
    });

    // Build upcoming lessons data
    const upcomingLessonsData: UpcomingLesson[] = lessons.slice(0, 4).map(lesson => ({
      time: lesson.startTime || '09:00',
      class: lesson.class?.name || 'Aula',
      students: lesson.class?._count?.enrollments || 0
    }));

    // Build recent activity (simplified - would come from audit logs in real implementation)
    const recentActivity = [
      { action: 'Novo aluno cadastrado', user: 'Sistema', time: '5 min atrás', icon: 'Users', color: 'text-blue-600' },
      { action: 'Presença registrada', user: 'Reconhecimento Facial', time: '15 min atrás', icon: 'CheckSquare', color: 'text-green-600' },
      { action: 'Nova turma criada', user: 'Administrador', time: '1 hora atrás', icon: 'Calendar', color: 'text-purple-600' },
      { action: 'Relatório gerado', user: 'Sistema', time: '2 horas atrás', icon: 'BarChart3', color: 'text-orange-600' },
    ];

    const dashboardData: DashboardData = {
      stats,
      classPerformance,
      upcomingLessons: upcomingLessonsData,
      recentActivity
    };

    return NextResponse.json({
      success: true,
      data: dashboardData
    });

  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}